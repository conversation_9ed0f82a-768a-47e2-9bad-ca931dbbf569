#!/usr/bin/env bash
# setup_local.sh — Setup HVAC-Remix CRM & GoBackend-Kratos locally

set -euo pipefail

# 1. CONFIGURATION ─────────────────────────────────────────────────────────────

# Change these if your repos live elsewhere
FRONTEND_REPO="https://github.com/your-org/hvac-remix.git"
BACKEND_REPO="https://github.com/your-org/GoBackend-Kratos.git"

# Directories (relative to this script)
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$BASE_DIR/hvac-remix"
BACKEND_DIR="$BASE_DIR/GoBackend-Kratos"

# Docker-Compose file (at project root)
DC_FILE="$BASE_DIR/docker-compose.yml"

# Environment file templates
FRONTEND_ENV_EXAMPLE="$FRONTEND_DIR/.env.example"
BACKEND_ENV_EXAMPLE="$BACKEND_DIR/.env.example"

# Ports
POSTGRES_CONTAINER="hvac-postgres"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"

# 2. PREREQUISITES CHECK ───────────────────────────────────────────────────────

for cmd in git docker docker-compose go yarn; do
  if ! command -v "$cmd" &> /dev/null; then
    echo "💥 ERROR: '$cmd' not found. Please install it before continuing." >&2
    exit 1
  fi
done

# 3. CLONE REPOSITORIES ────────────────────────────────────────────────────────

if [ ! -d "$FRONTEND_DIR" ]; then
  echo "🔧 Cloning frontend repo..."
  git clone "$FRONTEND_REPO" "$FRONTEND_DIR"
else
  echo "✔ Frontend directory exists, skipping clone."
fi

if [ ! -d "$BACKEND_DIR" ]; then
  echo "🔧 Cloning backend repo..."
  git clone "$BACKEND_REPO" "$BACKEND_DIR"
else
  echo "✔ Backend directory exists, skipping clone."
fi

# 4. COPY ENVIRONMENT FILES ───────────────────────────────────────────────────

if [ -f "$FRONTEND_ENV_EXAMPLE" ]; then
  cp -n "$FRONTEND_ENV_EXAMPLE" "$FRONTEND_DIR/.env"
  echo "✔ Copied frontend .env"
else
  echo "⚠️  No $FRONTEND_ENV_EXAMPLE found — please create your frontend .env manually."
fi

if [ -f "$BACKEND_ENV_EXAMPLE" ]; then
  cp -n "$BACKEND_ENV_EXAMPLE" "$BACKEND_DIR/.env"
  echo "✔ Copied backend .env"
else
  echo "⚠️  No $BACKEND_ENV_EXAMPLE found — please create your backend .env manually."
fi

# 5. START INFRASTRUCTURE ─────────────────────────────────────────────────────

if [ -f "$DC_FILE" ]; then
  echo "🚀 Bringing up Docker services..."
  docker-compose -f "$DC_FILE" up -d
else
  echo "⚠️  No docker-compose.yml found in $BASE_DIR — skipping docker-compose step."
fi

# 6. WAIT FOR POSTGRES ─────────────────────────────────────────────────────────

echo "⌛ Waiting for PostgreSQL to be ready on $POSTGRES_HOST:$POSTGRES_PORT..."
until pg_isready -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" &> /dev/null; do
  sleep 2
done
echo "✔ PostgreSQL is up."

# 7. DATABASE MIGRATIONS ───────────────────────────────────────────────────────

echo "🛠️  Running DB migrations..."
cd "$BACKEND_DIR"
# Replace below with your actual migration command
# e.g. go run cmd/migrate/main.go up
if [ -x "./migrate" ]; then
  ./migrate up
else
  echo "⚠️  No migrate binary found — please run your migrations manually."
fi

# 8. BUILD & START BACKEND ─────────────────────────────────────────────────────

echo "🛠️  Building Go backend..."
go build -o bin/hvac-backend ./cmd/server

echo "▶️  Starting Go backend (in background)..."
nohup ./bin/hvac-backend > backend.log 2>&1 &

# 9. INSTALL & START FRONTEND ──────────────────────────────────────────────────

echo "🛠️  Installing frontend dependencies..."
cd "$FRONTEND_DIR"
yarn install

echo "▶️  Starting frontend (http://localhost:3000)..."
nohup yarn dev > frontend.log 2>&1 &

# 10. FINAL MESSAGE ────────────────────────────────────────────────────────────

echo
echo "🎉 Setup complete!"
echo "   • Frontend ➜ http://localhost:3000"
echo "   • Backend  ➜ http://localhost:8000 (or as configured in .env)"
echo "   • Logs: backend.log, frontend.log"
echo